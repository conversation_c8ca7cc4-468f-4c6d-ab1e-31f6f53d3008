<script setup lang="ts">
import { ref, computed } from 'vue'
import { copyToClipboard } from '@/utils/clipboard.js'
import knowledgeApi from '@/api/knowledge'
// #ifdef H5
import MarkdownIt from 'markdown-it'
import htmlDocx from 'html-docx-js/dist/html-docx'
// #endif

interface Message {
  id: string
  type: 'user' | 'assistant'
  content?: string
  answer?: string
  liked?: boolean
  historyId?: string
}

interface Props {
  message: Message
}

const props = defineProps<Props>()

const emit = defineEmits<{
  delete: [id: string]
  like: [data: { message: Message; liked: boolean }]
}>()

// 响应式状态
const isVoicePlaying = ref(false)
const isDownloading = ref(false)

// 语音合成相关 - 在uni-app中使用原生API
let speechSynthesis: any = null
let speechUtterance: any = null

// 初始化语音合成
const initSpeech = () => {
  // #ifdef H5
  if (typeof window !== 'undefined' && window.speechSynthesis) {
    speechSynthesis = window.speechSynthesis
    speechUtterance = new SpeechSynthesisUtterance()
  }
  // #endif
}

// 提取纯文本内容
const extractPlainText = (text: string): string => {
  if (!text) return ''
  if (!text.includes('<')) {
    return text.trim()
  }
  return text.replace(/<[^>]*>/g, '').trim()
}

// 移除think标签的内容
const getContentWithoutThink = (text: string): string => {
  if (!text) return ''
  
  let content = text
  
  // 移除完整的think标签
  content = content.replace(/<think>[\s\S]*?<\/think>/g, '')
  
  // 移除未闭合的think标签
  content = content.replace(/<think>[\s\S]*$/, '')
  
  return content.trim()
}

// 复制功能
const handleCopy = async () => {
  const messageContent = props.message.content || props.message.answer || ''
  if (!messageContent) {
    uni.showToast({
      title: '没有可复制的内容',
      icon: 'none'
    })
    return
  }

  // 移除think标签后复制
  const contentToCopy = getContentWithoutThink(messageContent)
  if (!contentToCopy.trim()) {
    uni.showToast({
      title: '没有可复制的内容',
      icon: 'none'
    })
    return
  }

  try {
    // #ifdef H5
    await copyToClipboard(contentToCopy)
    uni.showToast({
      title: '复制成功',
      icon: 'success'
    })
    // #endif
    
    // #ifdef MP-WEIXIN || APP-PLUS
    uni.setClipboardData({
      data: contentToCopy,
      success: () => {
        uni.showToast({
          title: '复制成功',
          icon: 'success'
        })
      },
      fail: () => {
        uni.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
    // #endif
  } catch (error) {
    console.error('复制失败:', error)
    uni.showToast({
      title: '复制失败',
      icon: 'none'
    })
  }
}

// 删除功能
const handleDelete = () => {
  emit('delete', props.message.id)
}

// 语音播放功能
const handleVoicePlay = () => {
  // #ifdef H5
  if (!speechSynthesis || !speechUtterance) {
    initSpeech()
  }
  
  if (!speechSynthesis) {
    uni.showToast({
      title: '当前环境不支持语音播放',
      icon: 'none'
    })
    return
  }

  speechSynthesis.cancel()

  if (isVoicePlaying.value) {
    speechSynthesis.cancel()
    isVoicePlaying.value = false
  } else {
    const messageContent = props.message.content || props.message.answer || ''
    const plainText = extractPlainText(messageContent)
    if (!plainText) {
      uni.showToast({
        title: '没有可播放的文本内容',
        icon: 'none'
      })
      return
    }

    speechUtterance.text = plainText
    speechUtterance.lang = 'zh-CN'
    speechUtterance.volume = 1
    speechUtterance.rate = 1
    speechUtterance.pitch = 1

    speechUtterance.onend = () => {
      isVoicePlaying.value = false
    }

    speechUtterance.onerror = (event: any) => {
      console.error('语音播放错误:', event)
      isVoicePlaying.value = false
      
      if (event.error && event.error !== 'interrupted' && event.error !== 'canceled') {
        uni.showToast({
          title: '语音播放失败',
          icon: 'none'
        })
      }
    }

    speechSynthesis.speak(speechUtterance)
    isVoicePlaying.value = true
  }
  // #endif
  
  // #ifndef H5
  uni.showToast({
    title: '当前平台不支持语音播放',
    icon: 'none'
  })
  // #endif
}

// 点赞功能
const handleLike = async () => {
  try {
    // 检查是否有historyId，只有已保存的历史记录才能点赞
    if (!props.message.historyId) {
      uni.showToast({
        title: '只有已保存的历史记录才能点赞',
        icon: 'none'
      })
      return
    }

    const newLikedState = !props.message.liked

    // 调用点赞API - 使用PC端相同的参数格式
    const response = await knowledgeApi.giveThumbsUp({
      id: props.message.historyId,
      likeValue: newLikedState ? 1 : 0
    })

    if (response.code === 200) {
      emit('like', {
        message: props.message,
        liked: newLikedState
      })

      uni.showToast({
        title: newLikedState ? '点赞成功' : '取消点赞',
        icon: 'success'
      })
    } else {
      throw new Error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}



// 解析markdown内容并转换为Word段落
const parseMarkdownToWordParagraphs = (content: string) => {

  const lines = content.split('\n')
  const paragraphs = []
  let inCodeBlock = false
  let codeBlockContent = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const trimmedLine = line.trim()

    // 处理代码块
    if (trimmedLine.startsWith('```')) {
      if (inCodeBlock) {
        // 结束代码块，添加代码内容
        if (codeBlockContent.length > 0) {
          paragraphs.push(new Paragraph({
            children: [new TextRun({
              text: codeBlockContent.join('\n'),
              font: 'Courier New',
              size: 20
            })],
            indent: { left: 720 }
          }))
        }
        codeBlockContent = []
        inCodeBlock = false
      } else {
        // 开始代码块
        inCodeBlock = true
      }
      continue
    }

    if (inCodeBlock) {
      codeBlockContent.push(line)
      continue
    }

    if (!trimmedLine) {
      // 添加空行
      paragraphs.push(new Paragraph({ children: [new TextRun('')] }))
      continue
    }

    // 处理标题
    if (trimmedLine.startsWith('# ')) {
      paragraphs.push(new Paragraph({
        text: trimmedLine.substring(2),
        heading: HeadingLevel.HEADING_1
      }))
    } else if (trimmedLine.startsWith('## ')) {
      paragraphs.push(new Paragraph({
        text: trimmedLine.substring(3),
        heading: HeadingLevel.HEADING_2
      }))
    } else if (trimmedLine.startsWith('### ')) {
      paragraphs.push(new Paragraph({
        text: trimmedLine.substring(4),
        heading: HeadingLevel.HEADING_3
      }))
    } else if (trimmedLine.startsWith('#### ')) {
      paragraphs.push(new Paragraph({
        text: trimmedLine.substring(5),
        heading: HeadingLevel.HEADING_4
      }))
    } else if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ')) {
      // 处理列表项，解析其中的格式
      const listContent = trimmedLine.substring(2)
      const children = parseInlineFormatting(listContent)
      paragraphs.push(new Paragraph({
        children: [new TextRun('• '), ...children],
        indent: { left: 720 }
      }))
    } else if (/^\d+\.\s/.test(trimmedLine)) {
      // 处理有序列表
      const children = parseInlineFormatting(trimmedLine)
      paragraphs.push(new Paragraph({
        children: children,
        indent: { left: 720 }
      }))
    } else {
      // 处理普通段落，包含各种格式
      const children = parseInlineFormatting(trimmedLine)
      paragraphs.push(new Paragraph({ children }))
    }
  }

  return paragraphs
}

// 解析行内格式（粗体、斜体等）
const parseInlineFormatting = (text: string) => {
  const runs = []

  // 处理多种markdown格式
  const patterns = [
    { regex: /\*\*(.*?)\*\*/g, format: { bold: true } },      // 粗体 **text**
    { regex: /\*(.*?)\*/g, format: { italics: true } },       // 斜体 *text*
    { regex: /`(.*?)`/g, format: { font: 'Courier New' } },   // 行内代码 `code`
  ]

  let processedText = text
  let currentIndex = 0
  const segments = []

  // 找到所有格式标记的位置
  const matches = []
  patterns.forEach((pattern, patternIndex) => {
    let match
    const regex = new RegExp(pattern.regex.source, 'g')
    while ((match = regex.exec(text)) !== null) {
      matches.push({
        start: match.index,
        end: match.index + match[0].length,
        content: match[1],
        format: pattern.format,
        fullMatch: match[0]
      })
    }
  })

  // 按位置排序
  matches.sort((a, b) => a.start - b.start)

  // 处理重叠和构建文本段
  let lastEnd = 0
  for (const match of matches) {
    // 添加格式前的普通文本
    if (match.start > lastEnd) {
      const normalText = text.substring(lastEnd, match.start)
      if (normalText) {
        runs.push(new TextRun(normalText))
      }
    }

    // 添加格式化文本
    runs.push(new TextRun({
      text: match.content,
      ...match.format
    }))

    lastEnd = match.end
  }

  // 添加剩余的普通文本
  if (lastEnd < text.length) {
    const remainingText = text.substring(lastEnd)
    if (remainingText) {
      runs.push(new TextRun(remainingText))
    }
  }

  // 如果没有找到格式，返回普通文本
  if (runs.length === 0) {
    runs.push(new TextRun(text))
  }

  return runs
}

// #ifdef H5
// 初始化markdown-it，和PC端保持一致
const md = new MarkdownIt()
// #endif

// 下载功能
const handleDownload = async () => {
  if (isDownloading.value) return

  const messageContent = props.message.content || props.message.answer || ''
  if (!messageContent) {
    uni.showToast({
      title: '没有可下载的内容',
      icon: 'none'
    })
    return
  }

  // 移除think标签后获取内容，保持markdown格式
  const contentToDownload = getContentWithoutThink(messageContent)
  if (!contentToDownload.trim()) {
    uni.showToast({
      title: '没有可下载的内容',
      icon: 'none'
    })
    return
  }

  isDownloading.value = true

  try {
    // #ifdef H5
    // 使用markdown-it转为HTML，和PC端保持一致
    const htmlStr = md.render(contentToDownload)

    // 使用html-docx-js将HTML转换为Word文档
    const docxArrayBuffer = htmlDocx(htmlStr, null, {
      orientation: 'portrait'
    })
    const blob = new Blob([docxArrayBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })

    // 检查是否在APP的WebView中
    if (typeof window !== 'undefined' && (window as any).webkit && (window as any).webkit.messageHandlers) {
      // 在APP的WebView中，通过postMessage发送下载请求给原生APP
      const timestamp = new Date().getTime()
      const fileName = `message_${timestamp}.docx`

      // 将Blob转换为base64字符串
      const arrayBuffer = await blob.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)
      let binaryString = ''
      for (let i = 0; i < uint8Array.length; i++) {
        binaryString += String.fromCharCode(uint8Array[i])
      }
      const base64String = btoa(binaryString)

      window.parent.postMessage({
        action: 'download',
        fileName: fileName,
        content: base64String,
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        isBase64: true
      }, '*')

      uni.showToast({
        title: '下载请求已发送',
        icon: 'success'
      })
    } else {
      // 纯H5环境，直接下载
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      const timestamp = new Date().getTime()
      link.download = `message_${timestamp}.docx`
      link.href = url
      link.click()
      link.remove()
      URL.revokeObjectURL(url)

      uni.showToast({
        title: '下载成功',
        icon: 'success'
      })
    }
    // #endif

    // #ifndef H5
    // 原生APP环境，使用uni.saveFile
    const timestamp = new Date().getTime()
    const fileName = `message_${timestamp}.docx`

    // 先创建临时文件
    const fs = uni.getFileSystemManager()
    const tempFilePath = `${plus.io.convertLocalFileSystemURL('_doc/')}${fileName}`

    fs.writeFileSync(tempFilePath, contentToDownload, 'utf8')

    // 保存到用户目录
    uni.saveFile({
      tempFilePath: tempFilePath,
      success: () => {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
      },
      fail: () => {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
    // #endif
  } catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    })
  } finally {
    isDownloading.value = false
  }
}

// 组件销毁时清理语音
const cleanup = () => {
  // #ifdef H5
  if (speechSynthesis) {
    speechSynthesis.cancel()
  }
  // #endif
}

// 初始化
initSpeech()

// 生命周期
import { onUnmounted } from 'vue'
onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <view class="message-toolbar">
    <!-- 复制按钮 -->
    <view class="toolbar-button" @click="handleCopy">
      <image class="button-icon" src="../icons/copy.svg" mode="aspectFit" />
      <text class="button-text">复制</text>
    </view>

    <!-- 删除按钮 -->
    <view class="toolbar-button" @click="handleDelete">
      <image class="button-icon" src="../icons/delete.svg" mode="aspectFit" />
      <text class="button-text">删除</text>
    </view>

    <!-- 语音播放按钮 -->
    <view
      class="toolbar-button"
      :class="{ active: isVoicePlaying }"
      @click="handleVoicePlay"
    >
      <image class="button-icon" src="../icons/voice-play.svg" mode="aspectFit" />
      <text class="button-text">{{ isVoicePlaying ? '停止播放' : '语音播放' }}</text>
    </view>

    <!-- 点赞按钮 -->
    <view
      class="toolbar-button"
      :class="{ active: message.liked }"
      @click="handleLike"
    >
      <image class="button-icon" src="../icons/like.svg" mode="aspectFit" />
      <text class="button-text">{{ message.liked ? '取消点赞' : '点赞' }}</text>
    </view>

    <!-- 下载按钮 -->
    <view
      class="toolbar-button"
      :class="{ disabled: isDownloading }"
      @click="handleDownload"
    >
      <image class="button-icon" src="../icons/download.svg" mode="aspectFit" />
      <text class="button-text">{{ isDownloading ? '下载中...' : '下载' }}</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.message-toolbar {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #E8E8E8;
  margin-top: 24rpx;
  flex-wrap: wrap;
}

.toolbar-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: transparent;
  transition: background-color 0.2s ease;
  cursor: pointer;
  min-height: 60rpx;

  &:active {
    background-color: #F5F6F7;
  }

  &.active {
    color: $accent-color;
    background-color: rgba(20, 91, 255, 0.1);
    
    .button-icon {
      color: $accent-color;
    }
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.button-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}

.button-text {
  font-size: 26rpx;
  color: $text-primary;
  white-space: nowrap;
}

.active .button-text {
  color: $accent-color;
}
</style>
