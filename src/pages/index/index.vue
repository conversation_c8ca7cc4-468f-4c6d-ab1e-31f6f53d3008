<template>
  <view class="container">
    <!-- 自定义 loading 状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">页面加载中...</text>
    </view>

    <!-- web-view 组件，加载完成后显示 -->
    <web-view
      :src="webviewUrl"
      @load="onWebviewLoad"
      @error="onWebviewError"
      @message="onWebviewMessage"
      :class="{ 'webview-hidden': isLoading }"
      :webview-styles="webviewStyles"
    ></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoading: true,
      webviewUrl: 'http://*************:5173/',
      webviewStyles: {
        progress: {
          color: '#FF3333'
        }
      }
    }
  },
  methods: {
    // webview 加载完成事件
    onWebviewLoad() {
      console.log('webview 加载完成');
      this.isLoading = false;
    },

    // webview 加载错误事件
    onWebviewError(error) {
      console.error('webview 加载错误:', error);
      this.isLoading = false;
      // 可以在这里添加错误处理逻辑，比如显示错误提示
    },

    // webview 消息事件 - 处理来自H5的下载请求
    onWebviewMessage(event) {
      console.log('收到WebView消息:', event);

      try {
        const data = event.detail.data[0];
        console.log('解析消息数据:', data);

        if (data && data.action === 'download') {
          this.handleDownloadRequest(data);
        }
      } catch (error) {
        console.error('处理WebView消息失败:', error);
      }
    },

    // 处理下载请求
    async handleDownloadRequest(downloadData) {
      const { fileName, content, mimeType, isBase64 } = downloadData;

      console.log('开始处理下载:', fileName);

      uni.showLoading({
        title: '准备下载...'
      });

      try {
        // 直接使用文件系统API创建文件
        const fs = uni.getFileSystemManager();
        const timestamp = new Date().getTime();
        const tempFileName = `${timestamp}_${fileName}`;

        // 获取临时文件路径
        const tempFilePath = `${wx.env.USER_DATA_PATH}/${tempFileName}`;

        let fileContent = content;

        // 如果是base64格式的内容，需要解码
        if (isBase64) {
          // base64解码为二进制数据
          const binaryString = atob(content);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }

          // 写入二进制文件
          fs.writeFileSync(tempFilePath, bytes.buffer);
        } else if (fileName.endsWith('.docx')) {
          // 如果是docx文件但不是base64，创建简单的Word文档格式
          const paragraphs = content.split('\n').filter(line => line.trim());
          const wordParagraphs = paragraphs.map(paragraph =>
            `    <w:p>
      <w:r>
        <w:t>${paragraph.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')}</w:t>
      </w:r>
    </w:p>`
          ).join('\n');

          // 创建完整的Word文档XML结构
          fileContent = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:body>
${wordParagraphs}
  </w:body>
</w:document>`;

          // 写入文件内容
          fs.writeFileSync(tempFilePath, fileContent, 'utf8');
        } else {
          // 普通文本文件
          fs.writeFileSync(tempFilePath, fileContent, 'utf8');
        }

        // 保存文件到用户目录
        const savedPath = await new Promise((resolve, reject) => {
          uni.saveFile({
            tempFilePath: tempFilePath,
            success: (res) => resolve(res.savedFilePath),
            fail: (err) => {
              console.error('保存文件失败:', err);
              reject(err);
            }
          });
        });

        uni.hideLoading();

        uni.showModal({
          title: '下载成功',
          content: `文件已保存：${fileName}`,
          showCancel: false,
          confirmText: '确定'
        });

        console.log('文件保存成功:', savedPath);

      } catch (error) {
        console.error('下载失败:', error);
        uni.hideLoading();

        uni.showModal({
          title: '下载失败',
          content: `文件保存失败：${error.message || '请检查存储权限'}`,
          showCancel: false,
          confirmText: '确定'
        });
      }
    }
  }
}
</script>

<style>
.container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.webview-hidden {
  opacity: 0;
  position: absolute;
  top: -9999px;
  left: -9999px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
