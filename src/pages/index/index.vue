<template>
  <view class="container">
    <!-- 自定义 loading 状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">页面加载中...</text>
    </view>

    <!-- web-view 组件，加载完成后显示 -->
    <web-view
      :src="webviewUrl"
      @load="onWebviewLoad"
      @error="onWebviewError"
      @message="onWebviewMessage"
      :class="{ 'webview-hidden': isLoading }"
      :webview-styles="webviewStyles"
    ></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoading: true,
      webviewUrl: 'http://*************:5173/',
      webviewStyles: {
        progress: {
          color: '#FF3333'
        }
      }
    }
  },
  methods: {
    // webview 加载完成事件
    onWebviewLoad() {
      console.log('webview 加载完成');
      this.isLoading = false;
    },

    // webview 加载错误事件
    onWebviewError(error) {
      console.error('webview 加载错误:', error);
      this.isLoading = false;
      // 可以在这里添加错误处理逻辑，比如显示错误提示
    },

    // webview 消息事件 - 处理来自H5的下载请求
    onWebviewMessage(event) {
      console.log('收到WebView消息:', event);

      try {
        const data = event.detail.data[0];
        console.log('解析消息数据:', data);

        if (data && data.action === 'download') {
          this.handleDownloadRequest(data);
        }
      } catch (error) {
        console.error('处理WebView消息失败:', error);
      }
    },

    // 处理下载请求
    async handleDownloadRequest(downloadData) {
      const { fileName, content, mimeType } = downloadData;

      console.log('开始处理下载:', fileName);

      uni.showLoading({
        title: '准备下载...'
      });

      try {
        // 创建 Blob 数据
        const blob = new Blob([content], { type: mimeType || 'text/plain' });
        const url = URL.createObjectURL(blob);

        // 使用 uni.downloadFile 下载
        const downloadResult = await new Promise((resolve, reject) => {
          uni.downloadFile({
            url: url,
            success: (res) => {
              if (res.statusCode === 200) {
                resolve(res.tempFilePath);
              } else {
                reject(new Error('下载失败'));
              }
            },
            fail: reject
          });
        });

        // 保存文件到用户目录
        const savedPath = await new Promise((resolve, reject) => {
          uni.saveFile({
            tempFilePath: downloadResult,
            success: (res) => resolve(res.savedFilePath),
            fail: reject
          });
        });

        // 清理 Blob URL
        URL.revokeObjectURL(url);

        uni.hideLoading();

        uni.showModal({
          title: '下载成功',
          content: `文件已保存：${fileName}`,
          showCancel: false,
          confirmText: '确定'
        });

        console.log('文件保存成功:', savedPath);

      } catch (error) {
        console.error('下载失败:', error);
        uni.hideLoading();

        uni.showModal({
          title: '下载失败',
          content: '文件保存失败，请检查存储权限',
          showCancel: false,
          confirmText: '确定'
        });
      }
    }
  }
}
</script>

<style>
.container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.webview-hidden {
  opacity: 0;
  position: absolute;
  top: -9999px;
  left: -9999px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
